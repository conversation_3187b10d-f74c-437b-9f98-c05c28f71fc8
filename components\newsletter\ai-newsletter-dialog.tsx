"use client"

import * as React from "react"
import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Bot, Sparkles, Send, Globe } from "lucide-react"
import { Spinner } from "../ui/shadcn-io/spinner"
import { useLanguageContext } from "@/contexts/language-context"
import { useAIApi } from "@/hooks/use-ai-api"

interface AINewsletterDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  newsletterId: string
  onAIResponse: (response: any, language: string) => void
}

export function AINewsletterDialog({
  open,
  onOpenChange,
  newsletterId,
  onAIResponse
}: AINewsletterDialogProps) {
  const { selectedLanguage, languages } = useLanguageContext()
  const { generateNewsletterContent, loading: apiLoading, error: apiError } = useAIApi()
  const [prompt, setPrompt] = useState("")
  const [targetLanguage, setTargetLanguage] = useState("")

  // Set default target language when dialog opens
  React.useEffect(() => {
    if (open && !targetLanguage) {
      setTargetLanguage(selectedLanguage)
    }
  }, [open, selectedLanguage, targetLanguage])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (apiLoading || !targetLanguage) return

    try {
      const response = await generateNewsletterContent({
        newsletter_id: newsletterId,
        language: targetLanguage,
        prompt: prompt.trim() || "Generate engaging newsletter content for all blocks"
      })

      onAIResponse(response, targetLanguage)
      onOpenChange(false)
      setPrompt("")
    } catch (error) {
      console.error('Error calling AI newsletter generation API:', error)
      // Error is already handled by the useAIApi hook
    }
  }

  const handleCancel = () => {
    onOpenChange(false)
    setPrompt("")
  }

  // Get available languages
  const availableLanguages = ['ca', 'es', 'fr', 'en']

  const getLanguageDisplay = (langCode: string) => {
    const languageMap: Record<string, { display: string; color: string }> = {
      'es': { display: 'Espanyol', color: 'text-red-500' },
      'ca': { display: 'Català', color: 'text-yellow-500' },
      'fr': { display: 'Francès', color: 'text-blue-500' },
      'en': { display: 'Anglès', color: 'text-green-500' }
    }
    return languageMap[langCode] || { display: langCode.toUpperCase(), color: 'text-gray-500' }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Bot className="h-5 w-5 text-primary" />
            Generar contingut amb IA
          </DialogTitle>
          <DialogDescription>
            Genera contingut per a tota la newsletter utilitzant intel·ligència artificial. 
            L'IA crearà contingut coherent per a tots els blocs basant-se en les teves instruccions.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Language Selection */}
          <div className="space-y-2">
            <Label htmlFor="ai-language">Idioma de generació</Label>
            <Select value={targetLanguage} onValueChange={setTargetLanguage}>
              <SelectTrigger>
                <SelectValue placeholder="Selecciona un idioma" />
              </SelectTrigger>
              <SelectContent>
                {availableLanguages.map((lang) => {
                  const { display, color } = getLanguageDisplay(lang)
                  return (
                    <SelectItem key={lang} value={lang}>
                      <span className="flex items-center gap-2">
                        <Globe className={`h-3 w-3 ${color}`} />
                        <span className="ml-2">{display}</span>
                      </span>
                    </SelectItem>
                  )
                })}
              </SelectContent>
            </Select>
            <p className="text-xs text-muted-foreground">
              L'IA generarà contingut en l'idioma seleccionat per a tots els blocs de la newsletter.
            </p>
          </div>

          {/* Prompt Input */}
          <div className="space-y-2">
            <Label htmlFor="ai-prompt">Instruccions per a la IA</Label>
            <Textarea
              id="ai-prompt"
              placeholder="Descriu el contingut que vols generar. Per exemple: 'Crea una newsletter que promocioni un 10% de descuento en septiembre en los forfaits y la nueva pista negra en Grau Roig. Detalle de contenido por bloque: - Bloque 1: Destacar promoción del 10% de descuento en forfaits...'"
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              className="min-h-[120px] resize-none"
              disabled={apiLoading}
            />
            <p className="text-xs text-muted-foreground">
              Proporciona instruccions detallades sobre el contingut que vols generar per a cada bloc de la newsletter.
            </p>
          </div>

          {/* Error Display */}
          {apiError && (
            <div className="text-sm text-red-600 bg-red-50 p-3 rounded-md">
              {apiError}
            </div>
          )}
        </form>

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={handleCancel}
            disabled={apiLoading}
          >
            Cancel·lar
          </Button>
          <Button
            type="submit"
            onClick={handleSubmit}
            disabled={apiLoading || !targetLanguage}
            className="bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600"
          >
            {apiLoading ? (
              <>
                <Spinner className="mr-2 h-4 w-4" />
                Generant...
              </>
            ) : (
              <>
                <Sparkles className="mr-2 h-4 w-4" />
                Generar contingut
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
